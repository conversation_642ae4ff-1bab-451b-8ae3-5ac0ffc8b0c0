[{"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\layout.js": "1", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\login\\page.js": "2", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\signup\\page.js": "3", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\dashboard\\page.js": "4", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\layout.js": "5", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\listing\\page.js": "6", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\layout.js": "7", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\page.js": "8", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\bookkeeping.js": "9", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\clients.js": "10", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\utils.js": "11", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\hooks\\useAuth.js": "12", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\api\\auth.js": "13", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\axios.js": "14", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\tokenStorage.js": "15", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\validation.js": "16", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\book-closure\\page.js": "17", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\book-closure\\[clientId]\\page.js": "18", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\appointments\\page.js": "19", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\messages\\page.js": "20", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\report\\page.js": "21", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\settings\\page.js": "22", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\staff-utilization\\page.js": "23", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\dashboard.js": "24", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\configuration\\page.js": "25", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\add\\page.js": "26", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\edit\\[id]\\page.js": "27", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\page.js": "28", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\view\\[id]\\page.js": "29", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\add\\page.js": "30", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\edit\\[id]\\page.js": "31", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\page.js": "32", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\view\\[id]\\page.js": "33", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\add\\page.js": "34", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\edit\\[id]\\page.js": "35", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\page.js": "36", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\view\\[id]\\page.js": "37", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\add\\page.js": "38", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\edit\\[id]\\page.js": "39", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\page.js": "40", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\view\\[id]\\page.js": "41", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\add\\page.js": "42", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\edit\\[id]\\page.js": "43", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\page.js": "44", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\view\\[id]\\page.js": "45", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\organizations.js": "46", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\permissions.js": "47", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\roles.js": "48", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\tenants.js": "49", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\users.js": "50", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\common.js": "51", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\configuration.js": "52", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\index.js": "53", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\listing.js": "54", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\organization.js": "55", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\permission.js": "56", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\role.js": "57", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\tenant.js": "58", "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\user.js": "59"}, {"size": 136, "mtime": 1754297373852, "results": "60", "hashOfConfig": "61"}, {"size": 11492, "mtime": 1754567460926, "results": "62", "hashOfConfig": "61"}, {"size": 12576, "mtime": 1754307083701, "results": "63", "hashOfConfig": "61"}, {"size": 221, "mtime": 1754567460939, "results": "64", "hashOfConfig": "61"}, {"size": 777, "mtime": 1754569771406, "results": "65", "hashOfConfig": "61"}, {"size": 691, "mtime": 1754569761008, "results": "66", "hashOfConfig": "61"}, {"size": 2820, "mtime": 1754567460945, "results": "67", "hashOfConfig": "61"}, {"size": 918, "mtime": 1754561972491, "results": "68", "hashOfConfig": "61"}, {"size": 1179, "mtime": 1754567460957, "results": "69", "hashOfConfig": "61"}, {"size": 3170, "mtime": 1754567460962, "results": "70", "hashOfConfig": "61"}, {"size": 290, "mtime": 1754301091666, "results": "71", "hashOfConfig": "61"}, {"size": 7501, "mtime": 1754372804197, "results": "72", "hashOfConfig": "61"}, {"size": 1701, "mtime": 1754297373928, "results": "73", "hashOfConfig": "61"}, {"size": 1330, "mtime": 1754307083707, "results": "74", "hashOfConfig": "61"}, {"size": 6485, "mtime": 1754307083728, "results": "75", "hashOfConfig": "61"}, {"size": 2166, "mtime": 1754297373945, "results": "76", "hashOfConfig": "61"}, {"size": 1394, "mtime": 1754567460934, "results": "77", "hashOfConfig": "61"}, {"size": 374, "mtime": 1754903995466, "results": "78", "hashOfConfig": "61"}, {"size": 1407, "mtime": 1754561978531, "results": "79", "hashOfConfig": "61"}, {"size": 1463, "mtime": 1754545445803, "results": "80", "hashOfConfig": "61"}, {"size": 1434, "mtime": 1754545445869, "results": "81", "hashOfConfig": "61"}, {"size": 1915, "mtime": 1754896884437, "results": "82", "hashOfConfig": "61"}, {"size": 1605, "mtime": 1754545446032, "results": "83", "hashOfConfig": "61"}, {"size": 203, "mtime": 1754567460965, "results": "84", "hashOfConfig": "61"}, {"size": 11832, "mtime": 1754652710768, "results": "85", "hashOfConfig": "61"}, {"size": 820, "mtime": 1754893991414, "results": "86", "hashOfConfig": "61"}, {"size": 1409, "mtime": 1754894017255, "results": "87", "hashOfConfig": "61"}, {"size": 4603, "mtime": 1754894916306, "results": "88", "hashOfConfig": "61"}, {"size": 1263, "mtime": 1754894006084, "results": "89", "hashOfConfig": "61"}, {"size": 814, "mtime": 1754893699228, "results": "90", "hashOfConfig": "61"}, {"size": 1401, "mtime": 1754893726500, "results": "91", "hashOfConfig": "61"}, {"size": 4609, "mtime": 1754896797855, "results": "92", "hashOfConfig": "61"}, {"size": 1266, "mtime": 1754893714916, "results": "93", "hashOfConfig": "61"}, {"size": 763, "mtime": 1754896317236, "results": "94", "hashOfConfig": "61"}, {"size": 1269, "mtime": 1754893446200, "results": "95", "hashOfConfig": "61"}, {"size": 4720, "mtime": 1754893349511, "results": "96", "hashOfConfig": "61"}, {"size": 1140, "mtime": 1754893433727, "results": "97", "hashOfConfig": "61"}, {"size": 863, "mtime": 1754896342751, "results": "98", "hashOfConfig": "61"}, {"size": 1346, "mtime": 1754888645617, "results": "99", "hashOfConfig": "61"}, {"size": 4274, "mtime": 1754888490627, "results": "100", "hashOfConfig": "61"}, {"size": 1182, "mtime": 1754889474871, "results": "101", "hashOfConfig": "61"}, {"size": 743, "mtime": 1754894221826, "results": "102", "hashOfConfig": "61"}, {"size": 1269, "mtime": 1754894247330, "results": "103", "hashOfConfig": "61"}, {"size": 5440, "mtime": 1754894112126, "results": "104", "hashOfConfig": "61"}, {"size": 1140, "mtime": 1754894236929, "results": "105", "hashOfConfig": "61"}, {"size": 4180, "mtime": 1754896593626, "results": "106", "hashOfConfig": "61"}, {"size": 3902, "mtime": 1754896616410, "results": "107", "hashOfConfig": "61"}, {"size": 3094, "mtime": 1754896683537, "results": "108", "hashOfConfig": "61"}, {"size": 3154, "mtime": 1754896475528, "results": "109", "hashOfConfig": "61"}, {"size": 3811, "mtime": 1754896714799, "results": "110", "hashOfConfig": "61"}, {"size": 3692, "mtime": 1754570453412, "results": "111", "hashOfConfig": "61"}, {"size": 3222, "mtime": 1754570427859, "results": "112", "hashOfConfig": "61"}, {"size": 460, "mtime": 1754570463825, "results": "113", "hashOfConfig": "61"}, {"size": 1538, "mtime": 1754570332276, "results": "114", "hashOfConfig": "61"}, {"size": 1958, "mtime": 1754632521139, "results": "115", "hashOfConfig": "61"}, {"size": 2654, "mtime": 1754629488059, "results": "116", "hashOfConfig": "61"}, {"size": 1760, "mtime": 1754632286005, "results": "117", "hashOfConfig": "61"}, {"size": 2128, "mtime": 1754632456759, "results": "118", "hashOfConfig": "61"}, {"size": 1735, "mtime": 1754632259600, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ebbcc0", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\layout.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\login\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(auth)\\signup\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\dashboard\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\layout.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\listing\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\layout.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\bookkeeping.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\clients.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\utils.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\api\\auth.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\axios.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\tokenStorage.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\lib\\validation.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\book-closure\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\book-closure\\[clientId]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\appointments\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\messages\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\report\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\settings\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\staff-utilization\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\dashboard.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\configuration\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\add\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\edit\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\org\\view\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\add\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\edit\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\permission\\view\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\add\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\edit\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\role\\view\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\add\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\edit\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\tenant\\view\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\add\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\edit\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\masters\\user\\view\\[id]\\page.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\organizations.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\permissions.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\roles.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\tenants.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\data\\users.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\common.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\configuration.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\index.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\listing.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\organization.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\permission.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\role.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\tenant.js", [], [], "D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\utils\\const\\user.js", [], []]