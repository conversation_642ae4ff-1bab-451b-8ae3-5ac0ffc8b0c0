"use client";

import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ui/toast";

/**
 * Custom hook for data fetching with loading states and error handling
 * @param {Function} fetchFunction - Function that returns a promise with data
 * @param {Object} options - Configuration options
 * @param {boolean} options.immediate - Fetch data immediately on mount
 * @param {Array} options.dependencies - Dependencies to trigger refetch
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @param {boolean} options.showToast - Show toast notifications
 * @returns {Object} Data fetching utilities and state
 */
export const useDataFetching = (
  fetchFunction,
  {
    immediate = true,
    dependencies = [],
    onSuccess,
    onError,
    showToast = true,
  } = {}
) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);
  const { addToast } = useToast();

  const fetchData = useCallback(async (...args) => {
    if (!fetchFunction) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetchFunction(...args);
      setData(result);
      setLastFetch(new Date());

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message || "Failed to fetch data";
      setError(errorMessage);

      if (onError) {
        onError(err);
      }

      if (showToast) {
        addToast(errorMessage, "error");
      }

      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, onSuccess, onError, showToast, addToast]);

  const refetch = useCallback((...args) => {
    return fetchData(...args);
  }, [fetchData]);

  const clearData = useCallback(() => {
    setData(null);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Fetch data on mount or when dependencies change
  useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, [immediate, ...dependencies]);

  return {
    data,
    loading,
    error,
    lastFetch,
    fetchData,
    refetch,
    clearData,
    clearError,
    isStale: lastFetch && Date.now() - lastFetch.getTime() > 5 * 60 * 1000, // 5 minutes
  };
};

/**
 * Hook for paginated data fetching
 */
export const usePaginatedData = (
  fetchFunction,
  {
    pageSize = 10,
    initialPage = 1,
    ...options
  } = {}
) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  const wrappedFetchFunction = useCallback(async (...args) => {
    const result = await fetchFunction(currentPage, pageSize, ...args);
    
    // Assuming the API returns { data, totalPages, totalItems, currentPage }
    if (result.totalPages !== undefined) setTotalPages(result.totalPages);
    if (result.totalItems !== undefined) setTotalItems(result.totalItems);
    if (result.currentPage !== undefined) setCurrentPage(result.currentPage);
    
    return result.data || result;
  }, [fetchFunction, currentPage, pageSize]);

  const dataFetching = useDataFetching(wrappedFetchFunction, {
    ...options,
    dependencies: [currentPage, pageSize, ...(options.dependencies || [])],
  });

  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  }, [totalPages]);

  const nextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  const prevPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  const firstPage = useCallback(() => {
    goToPage(1);
  }, [goToPage]);

  const lastPage = useCallback(() => {
    goToPage(totalPages);
  }, [totalPages, goToPage]);

  return {
    ...dataFetching,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    goToPage,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
  };
};

/**
 * Hook for CRUD operations
 */
export const useCrudOperations = (
  {
    fetchList,
    fetchItem,
    createItem,
    updateItem,
    deleteItem,
  },
  options = {}
) => {
  const listData = useDataFetching(fetchList, options);
  const [selectedItem, setSelectedItem] = useState(null);
  const [itemLoading, setItemLoading] = useState(false);
  const { addToast } = useToast();

  const loadItem = useCallback(async (id) => {
    if (!fetchItem) return;

    setItemLoading(true);
    try {
      const item = await fetchItem(id);
      setSelectedItem(item);
      return item;
    } catch (error) {
      addToast(error.message || "Failed to load item", "error");
      throw error;
    } finally {
      setItemLoading(false);
    }
  }, [fetchItem, addToast]);

  const create = useCallback(async (data) => {
    if (!createItem) return;

    try {
      const result = await createItem(data);
      addToast("Item created successfully", "success");
      listData.refetch(); // Refresh the list
      return result;
    } catch (error) {
      addToast(error.message || "Failed to create item", "error");
      throw error;
    }
  }, [createItem, addToast, listData]);

  const update = useCallback(async (id, data) => {
    if (!updateItem) return;

    try {
      const result = await updateItem(id, data);
      addToast("Item updated successfully", "success");
      listData.refetch(); // Refresh the list
      if (selectedItem && selectedItem.id === id) {
        setSelectedItem(result);
      }
      return result;
    } catch (error) {
      addToast(error.message || "Failed to update item", "error");
      throw error;
    }
  }, [updateItem, addToast, listData, selectedItem]);

  const remove = useCallback(async (id) => {
    if (!deleteItem) return;

    try {
      await deleteItem(id);
      addToast("Item deleted successfully", "success");
      listData.refetch(); // Refresh the list
      if (selectedItem && selectedItem.id === id) {
        setSelectedItem(null);
      }
    } catch (error) {
      addToast(error.message || "Failed to delete item", "error");
      throw error;
    }
  }, [deleteItem, addToast, listData, selectedItem]);

  return {
    // List operations
    list: listData.data,
    listLoading: listData.loading,
    listError: listData.error,
    refreshList: listData.refetch,

    // Item operations
    selectedItem,
    itemLoading,
    loadItem,
    clearSelectedItem: () => setSelectedItem(null),

    // CRUD operations
    create,
    update,
    remove,
  };
};
