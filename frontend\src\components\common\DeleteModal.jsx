"use client";

import React from "react";
import { DeleteModal as BaseDeleteModal } from "./Modal";

export default function DeleteModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Delete Item",
  description = "Are you sure you want to delete this item? This action cannot be undone.",
  itemName = "",
  isLoading = false,
}) {
  // Enhanced description with item name
  const enhancedDescription = itemName
    ? `${description}\n\n"${itemName}"`
    : description;

  return (
    <BaseDeleteModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={title}
      description={enhancedDescription}
      isConfirmLoading={isLoading}
      confirmText={isLoading ? "Deleting..." : "Delete"}
    />
  );
}
