"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/masters/configuration/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/masters/configuration/page.js":
/*!***********************************************************!*\
  !*** ./src/app/(dashboard)/masters/configuration/page.js ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigurationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,Mail,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,Mail,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,Mail,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,Mail,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,Mail,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _components_common_PageWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/common/PageWrapper */ \"(app-pages-browser)/./src/components/common/PageWrapper.jsx\");\n/* harmony import */ var _components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/common/SettingsForm */ \"(app-pages-browser)/./src/components/common/SettingsForm.jsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.jsx\");\n/* harmony import */ var _utils_const_configuration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/const/configuration */ \"(app-pages-browser)/./src/utils/const/configuration.js\");\n/* harmony import */ var _data_configuration__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/configuration */ \"(app-pages-browser)/./src/data/configuration.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ConfigurationPage() {\n    _s();\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        siteName: \"CPA Pro Dashboard\",\n        siteDescription: \"Professional CPA Management System\",\n        timezone: \"America/New_York\",\n        dateFormat: \"MM/DD/YYYY\",\n        language: \"en\"\n    });\n    const [emailSettings, setEmailSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        smtpHost: \"smtp.gmail.com\",\n        smtpPort: \"587\",\n        smtpUsername: \"\",\n        smtpPassword: \"\",\n        fromEmail: \"<EMAIL>\",\n        fromName: \"CPA Pro\"\n    });\n    const [securitySettings, setSecuritySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sessionTimeout: \"30\",\n        passwordMinLength: \"8\",\n        requireTwoFactor: false,\n        allowPasswordReset: true,\n        maxLoginAttempts: \"5\"\n    });\n    const [systemSettings, setSystemSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maintenanceMode: false,\n        debugMode: false,\n        logLevel: \"info\",\n        backupFrequency: \"daily\",\n        maxFileSize: \"10\"\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        smsNotifications: false,\n        pushNotifications: true,\n        notificationFrequency: \"immediate\"\n    });\n    const handleSave = (section, values)=>{\n        console.log(\"Saving \".concat(section, \" settings:\"), values);\n        switch(section){\n            case \"general\":\n                setGeneralSettings(values);\n                break;\n            case \"email\":\n                setEmailSettings(values);\n                break;\n            case \"security\":\n                setSecuritySettings(values);\n                break;\n            case \"system\":\n                setSystemSettings(values);\n                break;\n            case \"notifications\":\n                setNotificationSettings(values);\n                break;\n        }\n    };\n    const settingsContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.Accordion, {\n            type: \"single\",\n            collapsible: true,\n            className: \"w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                    value: \"general\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"General Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fields: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.generalFields,\n                                initialValues: generalSettings,\n                                onSubmit: (values)=>handleSave(\"general\", values),\n                                submitLabel: \"Save General Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                    value: \"email\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Email Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fields: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.emailFields,\n                                initialValues: emailSettings,\n                                onSubmit: (values)=>handleSave(\"email\", values),\n                                submitLabel: \"Save Email Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                    value: \"security\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Security Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fields: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.securityFields,\n                                initialValues: securitySettings,\n                                onSubmit: (values)=>handleSave(\"security\", values),\n                                submitLabel: \"Save Security Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                    value: \"system\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"System Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fields: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.systemFields,\n                                initialValues: systemSettings,\n                                onSubmit: (values)=>handleSave(\"system\", values),\n                                submitLabel: \"Save System Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                    value: \"notifications\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_Mail_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Notification Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_4__.AccordionContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_SettingsForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fields: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.notificationFields,\n                                initialValues: notificationSettings,\n                                onSubmit: (values)=>handleSave(\"notifications\", values),\n                                submitLabel: \"Save Notification Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_PageWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: _utils_const_configuration__WEBPACK_IMPORTED_MODULE_5__.CONFIGURATION_CONSTANTS.PAGE_TITLE,\n        subtitle: _utils_const_configuration__WEBPACK_IMPORTED_MODULE_5__.CONFIGURATION_CONSTANTS.PAGE_SUBTITLE,\n        stats: _data_configuration__WEBPACK_IMPORTED_MODULE_6__.stats,\n        showAddButton: false,\n        showSearch: false,\n        showFilters: false,\n        data: [],\n        columns: [],\n        customContent: settingsContent\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\masters\\\\configuration\\\\page.js\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigurationPage, \"7u+H3KZKCXCB/0a0qC5y47FaDCU=\");\n_c = ConfigurationPage;\nvar _c;\n$RefreshReg$(_c, \"ConfigurationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/masters/configuration/page.js\n"));

/***/ })

});