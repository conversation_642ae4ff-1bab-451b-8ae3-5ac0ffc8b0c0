import { DataTypes } from 'sequelize';

const TokenModel = (sequelize) =>
  sequelize.define(
    'tokens',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      refreshToken: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      refreshTokenExpiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      resetToken: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      resetTokenExpiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      used: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      revoked: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      updatedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    },
    {
      tableName: 'tokens',
      timestamps: false,
      indexes: [
        {
          fields: ['userId'],
        },
        {
          fields: ['refreshToken'],
        },
        {
          fields: ['resetToken'],
        },
        {
          fields: ['refreshTokenExpiresAt'],
        },
        {
          fields: ['resetTokenExpiresAt'],
        },
        {
          fields: ['revoked'],
        },
        {
          fields: ['createdBy'],
        },
        {
          fields: ['updatedBy'],
        },
      ],
    }
  );

export default TokenModel; 