import { But<PERSON> } from "../ui/button";
import { <PERSON>, Pencil, Trash2 } from "lucide-react";

// Common status badge renderer
export const renderStatusBadge = (status, customColors = {}) => {
  const defaultColors = {
    Active: "bg-green-100 text-green-800",
    Inactive: "bg-red-100 text-red-800",
    Pending: "bg-yellow-100 text-yellow-800",
    Draft: "bg-gray-100 text-gray-800",
    Published: "bg-blue-100 text-blue-800",
    Archived: "bg-purple-100 text-purple-800",
  };

  const colors = { ...defaultColors, ...customColors };
  const colorClass = colors[status] || "bg-gray-100 text-gray-800";

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}
    >
      {status}
    </span>
  );
};

// Common role badge renderer
export const renderRoleBadge = (role) => {
  const roleColors = {
    "Super Admin": "bg-red-100 text-red-800",
    Admin: "bg-purple-100 text-purple-800",
    Manager: "bg-blue-100 text-blue-800",
    User: "bg-gray-100 text-gray-800",
    Editor: "bg-green-100 text-green-800",
    Viewer: "bg-yellow-100 text-yellow-800",
  };

  const colorClass = roleColors[role] || "bg-gray-100 text-gray-800";

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}
    >
      {role}
    </span>
  );
};

// Common type badge renderer
export const renderTypeBadge = (type) => {
  const typeColors = {
    Department: "bg-blue-100 text-blue-800",
    Team: "bg-green-100 text-green-800",
    Division: "bg-purple-100 text-purple-800",
    Branch: "bg-yellow-100 text-yellow-800",
    Create: "bg-green-100 text-green-800",
    Read: "bg-blue-100 text-blue-800",
    Update: "bg-yellow-100 text-yellow-800",
    Delete: "bg-red-100 text-red-800",
    Manage: "bg-purple-100 text-purple-800",
    Execute: "bg-indigo-100 text-indigo-800",
  };

  const colorClass = typeColors[type] || "bg-gray-100 text-gray-800";

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}
    >
      {type}
    </span>
  );
};

// Common action buttons renderer with icons
export const renderActionButtons = (item, onView, onEdit, onDelete) => {
  return (
    <div className="flex items-center space-x-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onView && onView(item)}
        className="h-8 w-8 p-0 hover:bg-blue-50"
        title="View"
      >
        <Eye className="h-4 w-4 text-blue-600" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onEdit && onEdit(item)}
        className="h-8 w-8 p-0 hover:bg-green-50"
        title="Edit"
      >
        <Pencil className="h-4 w-4 text-green-600" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onDelete && onDelete(item)}
        className="h-8 w-8 p-0 hover:bg-red-50"
        title="Delete"
      >
        <Trash2 className="h-4 w-4 text-red-600" />
      </Button>
    </div>
  );
};

// Common tags renderer (for multiple roles, permissions, etc.)
export const renderTags = (tags = [], maxVisible = 3) => {
  if (!Array.isArray(tags) || tags.length === 0) {
    return <span className="text-gray-400">None</span>;
  }

  const visibleTags = tags.slice(0, maxVisible);
  const remainingCount = tags.length - maxVisible;

  return (
    <div className="flex flex-wrap gap-1">
      {visibleTags.map((tag, index) => (
        <span
          key={index}
          className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs"
        >
          {tag}
        </span>
      ))}
      {remainingCount > 0 && (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
          +{remainingCount} more
        </span>
      )}
    </div>
  );
};

// Common date formatter
export const renderDate = (dateString, format = "short") => {
  if (!dateString) return "-";

  const date = new Date(dateString);

  if (format === "short") {
    return date.toLocaleDateString();
  } else if (format === "long") {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } else if (format === "relative") {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  }

  return date.toLocaleDateString();
};

// Common avatar/initials renderer
export const renderAvatar = (name, imageUrl = null, size = "sm") => {
  const sizeClasses = {
    sm: "w-8 h-8 text-xs",
    md: "w-10 h-10 text-sm",
    lg: "w-12 h-12 text-base",
  };

  const getInitials = (name) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (imageUrl) {
    return (
      <img
        src={imageUrl}
        alt={name}
        className={`${sizeClasses[size]} rounded-full object-cover`}
      />
    );
  }

  return (
    <div
      className={`${sizeClasses[size]} rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium`}
    >
      {getInitials(name)}
    </div>
  );
};

// Common number formatter
export const renderNumber = (number, format = "default") => {
  if (number === null || number === undefined) return "-";

  if (format === "currency") {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number);
  } else if (format === "percentage") {
    return `${number}%`;
  } else if (format === "compact") {
    return new Intl.NumberFormat("en-US", {
      notation: "compact",
      maximumFractionDigits: 1,
    }).format(number);
  }

  return new Intl.NumberFormat("en-US").format(number);
};
