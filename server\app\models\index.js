import { sequelize } from '../config/aws-config.js';
import RoleModel from './roles.model.js';
import UserModel from './user.model.js';
import TokenModel from './token.model.js';

const User = UserModel(sequelize);
const Role = RoleModel(sequelize);
const Token = TokenModel(sequelize);

// User-Role relationship
Role.hasMany(User, { foreignKey: 'role_id' });
User.belongsTo(Role, { foreignKey: 'role_id' });

// User-Token relationship
User.hasMany(Token, { foreignKey: 'userId' });
Token.belongsTo(User, { foreignKey: 'userId' });

// Self-referencing relationships for User audit fields
User.hasMany(User, { as: 'CreatedUsers', foreignKey: 'created_by' });
User.belongsTo(User, { as: 'CreatedBy', foreignKey: 'created_by' });

User.hasMany(User, { as: 'UpdatedUsers', foreignKey: 'updated_by' });
User.belongsTo(User, { as: 'UpdatedBy', foreignKey: 'updated_by' });

// Role audit field relationships (referencing User)
Role.belongsTo(User, { as: 'CreatedBy', foreignKey: 'created_by' });
Role.belongsTo(User, { as: 'UpdatedBy', foreignKey: 'updated_by' });

// Token audit field relationships (referencing User)
Token.belongsTo(User, { as: 'CreatedBy', foreignKey: 'createdBy' });
Token.belongsTo(User, { as: 'UpdatedBy', foreignKey: 'updatedBy' });

export { sequelize, Role, User, Token };
