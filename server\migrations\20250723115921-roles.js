'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('roles', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: 'TIMESTAMPTZ',
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
      },
      updated_at: {
        type: 'TIMESTAMPTZ',
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex('roles', ['created_by'], {
      name: 'roles_created_by_idx'
    });
    await queryInterface.addIndex('roles', ['updated_by'], {
      name: 'roles_updated_by_idx'
    });

    // Add indexes for better performance
    await queryInterface.addIndex('roles', ['created_by']);
    await queryInterface.addIndex('roles', ['updated_by']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('roles');
  }
};
