import React, { useState } from "react";
import PdfViewer from "@/components/dashboard/PDFViewer";
import { FinancePath, monthToFile } from "@/data/dashboard";
import Sidebar from "./Sidebar";
import { DASHBOARD_CONSTANTS } from "@/utils/const/dashboard";

export default function Dashboard() {
  const [months, setMonths] = useState(DASHBOARD_CONSTANTS.SIDEBAR.MONTHS[3]);
  const [pageToView, setPageToView] = useState(1);
  const [isFinancialSelected, setIsFinancialSelected] = useState(false);
  const pdfPath = isFinancialSelected ? FinancePath : monthToFile[months] || "";
  const page = isFinancialSelected ? pageToView : 1;

  if (!pdfPath) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        {DASHBOARD_CONSTANTS.NO_PDF_MESSAGE}
      </div>
    );
  }

  const handleDownload = async () => {
    if (!pdfPath) return;
    try {
      const response = await fetch(pdfPath);
      if (!response.ok) throw new Error(DASHBOARD_CONSTANTS.DOWNLOAD_ERROR);

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = DASHBOARD_CONSTANTS.DEFAULT_DOWNLOAD_FILENAME;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert(DASHBOARD_CONSTANTS.DOWNLOAD_ERROR);
    }
  };

  return (
    <div className="flex w-full h-full min-h-0" style={{ height: "100vh" }}>
      <Sidebar
        months={months}
        setMonths={setMonths}
        pageToView={pageToView}
        setPageToView={setPageToView}
        isFinancialSelected={isFinancialSelected}
        setIsFinancialSelected={setIsFinancialSelected}
        onDownload={handleDownload}
      />
      <div className="flex-1 h-full overflow-y-auto overflow-x-hidden flex items-start justify-center bg-gray-50 min-w-0">
        <PdfViewer url={pdfPath} pageToView={page} />
      </div>
    </div>
  );
}
