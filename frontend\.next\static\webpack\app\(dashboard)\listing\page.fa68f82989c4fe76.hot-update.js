"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/listing/page",{

/***/ "(app-pages-browser)/./src/utils/const/auth.js":
/*!*********************************!*\
  !*** ./src/utils/const/auth.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_CONSTANTS: () => (/* binding */ AUTH_CONSTANTS)\n/* harmony export */ });\n// Authentication Page Constants\nconst AUTH_CONSTANTS = {\n    // Page Titles and Headers\n    LOGIN: {\n        PAGE_TITLE: \"Login\",\n        PAGE_SUBTITLE: \"Sign in to your CPA dashboard\",\n        FORM_TITLE: \"Welcome Back\",\n        FORM_SUBTITLE: \"Please sign in to your account\"\n    },\n    SIGNUP: {\n        PAGE_TITLE: \"Sign Up\",\n        PAGE_SUBTITLE: \"Create your CPA dashboard account\",\n        FORM_TITLE: \"Create Account\",\n        FORM_SUBTITLE: \"Get started with your CPA dashboard\"\n    },\n    FORGOT_PASSWORD: {\n        PAGE_TITLE: \"Forgot Password\",\n        PAGE_SUBTITLE: \"Reset your password\",\n        FORM_TITLE: \"Reset Password\",\n        FORM_SUBTITLE: \"Enter your email to receive reset instructions\"\n    },\n    RESET_PASSWORD: {\n        PAGE_TITLE: \"Reset Password\",\n        PAGE_SUBTITLE: \"Create a new password\",\n        FORM_TITLE: \"New Password\",\n        FORM_SUBTITLE: \"Enter your new password\"\n    },\n    // Form Labels\n    LABELS: {\n        EMAIL: \"Email / Username\",\n        PASSWORD: \"Password\",\n        CONFIRM_PASSWORD: \"Confirm Password\",\n        REMEMBER_ME: \"Remember me\",\n        FIRST_NAME: \"First Name\",\n        LAST_NAME: \"Last Name\",\n        COMPANY_NAME: \"Company Name\",\n        PHONE: \"Phone Number\"\n    },\n    // Placeholders\n    PLACEHOLDERS: {\n        EMAIL: \"Enter your email or username\",\n        PASSWORD: \"Enter your password\",\n        CONFIRM_PASSWORD: \"Confirm your password\",\n        FIRST_NAME: \"Enter your first name\",\n        LAST_NAME: \"Enter your last name\",\n        COMPANY_NAME: \"Enter your company name\",\n        PHONE: \"Enter your phone number\"\n    },\n    // Button Text\n    BUTTONS: {\n        LOGIN: \"Log In\",\n        SIGNUP: \"Sign Up\",\n        FORGOT_PASSWORD: \"Forgot Password?\",\n        RESET_PASSWORD: \"Reset Password\",\n        BACK_TO_LOGIN: \"Back to Login\",\n        SEND_RESET_LINK: \"Send Reset Link\",\n        SIGNING_IN: \"Signing In...\",\n        CREATING_ACCOUNT: \"Creating Account...\",\n        SENDING: \"Sending...\",\n        RESETTING: \"Resetting...\"\n    },\n    // Validation Messages\n    VALIDATION: {\n        EMAIL_REQUIRED: \"Please fill in all required fields\",\n        INVALID_EMAIL: \"Please enter a valid email address\",\n        PASSWORD_REQUIRED: \"Password is required\",\n        PASSWORD_MISMATCH: \"Passwords do not match\",\n        WEAK_PASSWORD: \"Password must be at least 8 characters with uppercase, lowercase, number, and special character\",\n        FIRST_NAME_REQUIRED: \"First name is required\",\n        LAST_NAME_REQUIRED: \"Last name is required\",\n        COMPANY_NAME_REQUIRED: \"Company name is required\",\n        PHONE_REQUIRED: \"Phone number is required\",\n        INVALID_PHONE: \"Please enter a valid phone number\"\n    },\n    // Success Messages\n    SUCCESS: {\n        LOGIN: \"Successfully logged in\",\n        SIGNUP: \"Account created successfully\",\n        PASSWORD_RESET_SENT: \"Password reset link sent to your email\",\n        PASSWORD_RESET: \"Password reset successfully\",\n        LOGOUT: \"Successfully logged out\"\n    },\n    // Error Messages\n    ERRORS: {\n        LOGIN_FAILED: \"Invalid email or password\",\n        SIGNUP_FAILED: \"Failed to create account\",\n        EMAIL_EXISTS: \"Email already exists\",\n        NETWORK_ERROR: \"Network error. Please try again.\",\n        SERVER_ERROR: \"Server error. Please try again later.\",\n        INVALID_TOKEN: \"Invalid or expired reset token\",\n        PASSWORD_RESET_FAILED: \"Failed to reset password\"\n    },\n    // Links and Navigation\n    LINKS: {\n        SIGNUP: \"Don't have an account? Sign up\",\n        LOGIN: \"Already have an account? Sign in\",\n        FORGOT_PASSWORD: \"Forgot your password?\",\n        TERMS: \"Terms of Service\",\n        PRIVACY: \"Privacy Policy\"\n    },\n    // Support and Contact\n    SUPPORT: {\n        NEED_HELP: \"Need help? Contact support at\",\n        EMAIL: \"<EMAIL>\",\n        SSL_MESSAGE: \"Secure login protected by SSL encryption\"\n    },\n    // Company Branding\n    BRANDING: {\n        COMPANY_NAME: \"BG ADVISORS CPA, LTD.\",\n        POWERED_BY: \"Powered by\",\n        PROVIDER: \"GetOnData Solutions\",\n        LOGO_ALT: \"BG ADVISORS CPA, LTD.\",\n        PROVIDER_LOGO_ALT: \"BG Advisors CPA Logo\"\n    },\n    // Loading States\n    LOADING: {\n        SIGNING_IN: \"Signing in...\",\n        CREATING_ACCOUNT: \"Creating account...\",\n        SENDING_RESET: \"Sending reset link...\",\n        RESETTING_PASSWORD: \"Resetting password...\",\n        VERIFYING: \"Verifying...\"\n    },\n    // Form Validation Patterns\n    PATTERNS: {\n        EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n        PHONE: /^\\+?[\\d\\s\\-\\(\\)]{10,}$/,\n        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/\n    },\n    // Image Assets\n    IMAGES: {\n        LOGO: \"/logo.png\",\n        LOGIN_ILLUSTRATION: \"/login.png\",\n        BACKGROUND: \"/bg.jpg\",\n        PROVIDER_LOGO: \"/GetOnData.png\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/const/auth.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/const/index.js":
/*!**********************************!*\
  !*** ./src/utils/const/index.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_CONSTANTS: () => (/* reexport safe */ _auth_js__WEBPACK_IMPORTED_MODULE_8__.AUTH_CONSTANTS),\n/* harmony export */   COMMON_CONSTANTS: () => (/* reexport safe */ _common_js__WEBPACK_IMPORTED_MODULE_7__.COMMON_CONSTANTS),\n/* harmony export */   CONFIGURATION_CONSTANTS: () => (/* reexport safe */ _configuration_js__WEBPACK_IMPORTED_MODULE_6__.CONFIGURATION_CONSTANTS),\n/* harmony export */   LISTING_CONSTANTS: () => (/* reexport safe */ _listing_js__WEBPACK_IMPORTED_MODULE_0__.LISTING_CONSTANTS),\n/* harmony export */   MESSAGES: () => (/* reexport safe */ _messages_js__WEBPACK_IMPORTED_MODULE_9__.MESSAGES),\n/* harmony export */   ORGANIZATION_CONSTANTS: () => (/* reexport safe */ _organization_js__WEBPACK_IMPORTED_MODULE_3__.ORGANIZATION_CONSTANTS),\n/* harmony export */   PERMISSION_CONSTANTS: () => (/* reexport safe */ _permission_js__WEBPACK_IMPORTED_MODULE_4__.PERMISSION_CONSTANTS),\n/* harmony export */   ROLE_CONSTANTS: () => (/* reexport safe */ _role_js__WEBPACK_IMPORTED_MODULE_2__.ROLE_CONSTANTS),\n/* harmony export */   TENANT_CONSTANTS: () => (/* reexport safe */ _tenant_js__WEBPACK_IMPORTED_MODULE_5__.TENANT_CONSTANTS),\n/* harmony export */   USER_CONSTANTS: () => (/* reexport safe */ _user_js__WEBPACK_IMPORTED_MODULE_1__.USER_CONSTANTS)\n/* harmony export */ });\n/* harmony import */ var _listing_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./listing.js */ \"(app-pages-browser)/./src/utils/const/listing.js\");\n/* harmony import */ var _user_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./user.js */ \"(app-pages-browser)/./src/utils/const/user.js\");\n/* harmony import */ var _role_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./role.js */ \"(app-pages-browser)/./src/utils/const/role.js\");\n/* harmony import */ var _organization_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./organization.js */ \"(app-pages-browser)/./src/utils/const/organization.js\");\n/* harmony import */ var _permission_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./permission.js */ \"(app-pages-browser)/./src/utils/const/permission.js\");\n/* harmony import */ var _tenant_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tenant.js */ \"(app-pages-browser)/./src/utils/const/tenant.js\");\n/* harmony import */ var _configuration_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./configuration.js */ \"(app-pages-browser)/./src/utils/const/configuration.js\");\n/* harmony import */ var _common_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./common.js */ \"(app-pages-browser)/./src/utils/const/common.js\");\n/* harmony import */ var _auth_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./auth.js */ \"(app-pages-browser)/./src/utils/const/auth.js\");\n/* harmony import */ var _messages_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./messages.js */ \"(app-pages-browser)/./src/utils/const/messages.js\");\n// Export all constants from a central location\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9jb25zdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLCtDQUErQztBQUNFO0FBQ047QUFDQTtBQUNnQjtBQUNKO0FBQ1I7QUFDYztBQUNkO0FBQ0o7QUFDRiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxDcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxmcm9udGVuZFxcc3JjXFx1dGlsc1xcY29uc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBhbGwgY29uc3RhbnRzIGZyb20gYSBjZW50cmFsIGxvY2F0aW9uXHJcbmV4cG9ydCB7IExJU1RJTkdfQ09OU1RBTlRTIH0gZnJvbSBcIi4vbGlzdGluZy5qc1wiO1xyXG5leHBvcnQgeyBVU0VSX0NPTlNUQU5UUyB9IGZyb20gXCIuL3VzZXIuanNcIjtcclxuZXhwb3J0IHsgUk9MRV9DT05TVEFOVFMgfSBmcm9tIFwiLi9yb2xlLmpzXCI7XHJcbmV4cG9ydCB7IE9SR0FOSVpBVElPTl9DT05TVEFOVFMgfSBmcm9tIFwiLi9vcmdhbml6YXRpb24uanNcIjtcclxuZXhwb3J0IHsgUEVSTUlTU0lPTl9DT05TVEFOVFMgfSBmcm9tIFwiLi9wZXJtaXNzaW9uLmpzXCI7XHJcbmV4cG9ydCB7IFRFTkFOVF9DT05TVEFOVFMgfSBmcm9tIFwiLi90ZW5hbnQuanNcIjtcclxuZXhwb3J0IHsgQ09ORklHVVJBVElPTl9DT05TVEFOVFMgfSBmcm9tIFwiLi9jb25maWd1cmF0aW9uLmpzXCI7XHJcbmV4cG9ydCB7IENPTU1PTl9DT05TVEFOVFMgfSBmcm9tIFwiLi9jb21tb24uanNcIjtcclxuZXhwb3J0IHsgQVVUSF9DT05TVEFOVFMgfSBmcm9tIFwiLi9hdXRoLmpzXCI7XHJcbmV4cG9ydCB7IE1FU1NBR0VTIH0gZnJvbSBcIi4vbWVzc2FnZXMuanNcIjtcclxuIl0sIm5hbWVzIjpbIkxJU1RJTkdfQ09OU1RBTlRTIiwiVVNFUl9DT05TVEFOVFMiLCJST0xFX0NPTlNUQU5UUyIsIk9SR0FOSVpBVElPTl9DT05TVEFOVFMiLCJQRVJNSVNTSU9OX0NPTlNUQU5UUyIsIlRFTkFOVF9DT05TVEFOVFMiLCJDT05GSUdVUkFUSU9OX0NPTlNUQU5UUyIsIkNPTU1PTl9DT05TVEFOVFMiLCJBVVRIX0NPTlNUQU5UUyIsIk1FU1NBR0VTIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/const/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/const/messages.js":
/*!*************************************!*\
  !*** ./src/utils/const/messages.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES)\n/* harmony export */ });\n// Centralized Messages for User Interface\nconst MESSAGES = {\n    // General Success Messages\n    SUCCESS: {\n        SAVE: \"Changes saved successfully\",\n        CREATE: \"Item created successfully\",\n        UPDATE: \"Item updated successfully\",\n        DELETE: \"Item deleted successfully\",\n        UPLOAD: \"File uploaded successfully\",\n        DOWNLOAD: \"File downloaded successfully\",\n        SYNC: \"Data synchronized successfully\",\n        SUBMIT: \"Form submitted successfully\",\n        COPY: \"Copied to clipboard\",\n        EXPORT: \"Data exported successfully\",\n        IMPORT: \"Data imported successfully\"\n    },\n    // General Error Messages\n    ERROR: {\n        GENERAL: \"An error occurred. Please try again.\",\n        NETWORK: \"Network error. Please check your connection.\",\n        SERVER: \"Server error. Please try again later.\",\n        UNAUTHORIZED: \"You are not authorized to perform this action.\",\n        FORBIDDEN: \"Access denied.\",\n        NOT_FOUND: \"The requested item was not found.\",\n        TIMEOUT: \"Request timed out. Please try again.\",\n        VALIDATION: \"Please check your input and try again.\",\n        SAVE_FAILED: \"Failed to save changes\",\n        DELETE_FAILED: \"Failed to delete item\",\n        UPLOAD_FAILED: \"Failed to upload file\",\n        DOWNLOAD_FAILED: \"Failed to download file\",\n        SYNC_FAILED: \"Failed to synchronize data\",\n        EXPORT_FAILED: \"Failed to export data\",\n        IMPORT_FAILED: \"Failed to import data\"\n    },\n    // Loading States\n    LOADING: {\n        DEFAULT: \"Loading...\",\n        SAVING: \"Saving...\",\n        DELETING: \"Deleting...\",\n        UPLOADING: \"Uploading...\",\n        DOWNLOADING: \"Downloading...\",\n        SYNCING: \"Syncing...\",\n        PROCESSING: \"Processing...\",\n        SUBMITTING: \"Submitting...\",\n        EXPORTING: \"Exporting...\",\n        IMPORTING: \"Importing...\",\n        FETCHING: \"Fetching data...\",\n        UPDATING: \"Updating...\"\n    },\n    // Confirmation Messages\n    CONFIRM: {\n        DELETE: \"Are you sure you want to delete this item?\",\n        DELETE_MULTIPLE: \"Are you sure you want to delete these items?\",\n        UNSAVED_CHANGES: \"You have unsaved changes. Are you sure you want to leave?\",\n        RESET_FORM: \"Are you sure you want to reset the form?\",\n        CANCEL_OPERATION: \"Are you sure you want to cancel this operation?\",\n        LOGOUT: \"Are you sure you want to log out?\",\n        CLEAR_DATA: \"Are you sure you want to clear all data?\",\n        OVERRIDE: \"This will override existing data. Continue?\"\n    },\n    // Validation Messages\n    VALIDATION: {\n        REQUIRED: \"This field is required\",\n        INVALID_EMAIL: \"Please enter a valid email address\",\n        INVALID_PHONE: \"Please enter a valid phone number\",\n        INVALID_URL: \"Please enter a valid URL\",\n        INVALID_DATE: \"Please enter a valid date\",\n        INVALID_NUMBER: \"Please enter a valid number\",\n        MIN_LENGTH: \"Must be at least {min} characters\",\n        MAX_LENGTH: \"Must be no more than {max} characters\",\n        PASSWORD_MISMATCH: \"Passwords do not match\",\n        WEAK_PASSWORD: \"Password is too weak\",\n        INVALID_FILE_TYPE: \"Invalid file type\",\n        FILE_TOO_LARGE: \"File size is too large\",\n        DUPLICATE_ENTRY: \"This entry already exists\"\n    },\n    // Empty States\n    EMPTY: {\n        NO_DATA: \"No data available\",\n        NO_RESULTS: \"No results found\",\n        NO_ITEMS: \"No items to display\",\n        NO_FILES: \"No files uploaded\",\n        NO_MESSAGES: \"No messages\",\n        NO_NOTIFICATIONS: \"No notifications\",\n        SEARCH_NO_RESULTS: \"No results found for your search\",\n        FILTER_NO_RESULTS: \"No items match your filters\"\n    },\n    // Action Feedback\n    FEEDBACK: {\n        COPIED: \"Copied to clipboard\",\n        SAVED_DRAFT: \"Draft saved\",\n        AUTO_SAVED: \"Auto-saved\",\n        CHANGES_DETECTED: \"Changes detected\",\n        UP_TO_DATE: \"Everything is up to date\",\n        SYNC_COMPLETE: \"Sync complete\",\n        OPERATION_COMPLETE: \"Operation completed\",\n        PROCESSING_COMPLETE: \"Processing complete\"\n    },\n    // Navigation and Actions\n    NAVIGATION: {\n        BACK: \"Go back\",\n        NEXT: \"Next\",\n        PREVIOUS: \"Previous\",\n        CONTINUE: \"Continue\",\n        FINISH: \"Finish\",\n        SKIP: \"Skip\",\n        RETRY: \"Retry\",\n        REFRESH: \"Refresh\",\n        RELOAD: \"Reload page\"\n    },\n    // File Operations\n    FILE: {\n        UPLOAD_PROMPT: \"Click to upload or drag and drop\",\n        UPLOAD_FORMATS: \"Supported formats: {formats}\",\n        UPLOAD_SIZE_LIMIT: \"Maximum file size: {size}\",\n        PROCESSING: \"Processing file...\",\n        INVALID_FORMAT: \"Invalid file format\",\n        TOO_LARGE: \"File is too large\",\n        UPLOAD_SUCCESS: \"File uploaded successfully\",\n        UPLOAD_ERROR: \"Failed to upload file\",\n        DOWNLOAD_START: \"Download started\",\n        DOWNLOAD_COMPLETE: \"Download complete\"\n    },\n    // Search and Filter\n    SEARCH: {\n        PLACEHOLDER: \"Search...\",\n        NO_RESULTS: \"No results found\",\n        SEARCHING: \"Searching...\",\n        CLEAR_SEARCH: \"Clear search\",\n        SEARCH_RESULTS: \"{count} results found\",\n        FILTER_APPLIED: \"Filter applied\",\n        FILTER_CLEARED: \"Filter cleared\",\n        SORT_APPLIED: \"Sort applied\"\n    },\n    // Permissions and Access\n    PERMISSIONS: {\n        ACCESS_DENIED: \"You don't have permission to access this resource\",\n        INSUFFICIENT_PERMISSIONS: \"Insufficient permissions\",\n        LOGIN_REQUIRED: \"Please log in to continue\",\n        SESSION_EXPIRED: \"Your session has expired. Please log in again\",\n        ACCOUNT_DISABLED: \"Your account has been disabled\",\n        FEATURE_DISABLED: \"This feature is currently disabled\"\n    },\n    // System Status\n    STATUS: {\n        ONLINE: \"Online\",\n        OFFLINE: \"Offline\",\n        CONNECTING: \"Connecting...\",\n        CONNECTED: \"Connected\",\n        DISCONNECTED: \"Disconnected\",\n        SYNCED: \"Synced\",\n        OUT_OF_SYNC: \"Out of sync\",\n        MAINTENANCE: \"System maintenance in progress\",\n        UPDATED: \"System updated\"\n    },\n    // Time and Date\n    TIME: {\n        JUST_NOW: \"Just now\",\n        MINUTES_AGO: \"{minutes} minutes ago\",\n        HOURS_AGO: \"{hours} hours ago\",\n        DAYS_AGO: \"{days} days ago\",\n        WEEKS_AGO: \"{weeks} weeks ago\",\n        MONTHS_AGO: \"{months} months ago\",\n        LAST_UPDATED: \"Last updated: {time}\",\n        NEVER: \"Never\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/const/messages.js\n"));

/***/ })

});