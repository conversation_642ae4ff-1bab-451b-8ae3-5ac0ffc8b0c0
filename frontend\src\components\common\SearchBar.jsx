import React from "react";
import { Search } from "lucide-react";

export default function SearchBar({
  value,
  onChange,
  placeholder = "Search...",
  className = "",
  disabled = false,
  onClear,
  showClearButton = false,
}) {
  return (
    <div className={`relative w-full max-w-md ${className}`}>
      <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-[#7C7C9A]" />
      <input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className="w-full pl-10 pr-4 py-3 rounded-xl border-none shadow-md bg-white text-[#7C7C9A] placeholder-[#7C7C9A] focus:outline-none focus:ring-2 focus:ring-[#6C63FF] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      />
      {showClearButton && value && (
        <button
          onClick={onClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#7C7C9A] hover:text-[#6C63FF] transition-colors duration-200"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
}
